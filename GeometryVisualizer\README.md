# Geometry Visualizer

WPF застосунок для візуалізації геометрії з JSON файлів, створених основним проектом PathwayLowVoltagePlan.

## Функціональність

- Завантаження JSON файлів з геометрією
- Візуалізація ліній з різними кольорами та адаптивною товщиною
- Відображення точок як жовті кружечки
- **Необмежене масштабування** (від 0.01x до 1000x) для детального перегляду
- **Швидке масштабування колесом миші** (3x швидше, експоненціальне)
- **Ручне введення масштабу** та кнопки +/- для точного контролю
- Прокрутка та перетягування мишею для навігації
- Скидання виду (Reset View)
- Автоматичне підгонка під розмір вікна (Fit to Window)
- **Окреме поле для координат миші** в реальному часі (4 знаки після коми)
- **Перемикач відображення анотацій** (Show Labels)
- **Інструмент лінійка** для вимірювання відстаней
- **Детальні анотації координат** на всіх точках ліній та геометричних об'єктах
- Адаптивні розміри елементів залежно від масштабу
- Незалежність від концепції "outlets" - працює з будь-якими точками

## Як використовувати

### Основні операції
1. Запустіть основний проект PathwayLowVoltagePlan для створення geometry.json
2. Запустіть GeometryVisualizer
3. Вкажіть шлях до JSON файлу або використайте кнопку "Browse..."
4. Натисніть "Load" для завантаження геометрії

### Масштабування
5. **Колесо миші**: Швидке масштабування (експоненціальне, 3x швидше)
6. **Слайдер Zoom**: Плавне масштабування від 0.01x до 1000x
7. **Поле введення**: Введіть точне значення масштабу та натисніть Enter
8. **Кнопки +/-**: Покрокове збільшення/зменшення масштабу
9. **Fit to Window**: Автоматичний підбір оптимального масштабу

### Анотації та вимірювання
10. **"Show Labels"**: Вимкніть для кращого перегляду геометрії без анотацій
11. **Інструмент лінійка**: Натисніть "📏 Measure", клікніть дві точки для вимірювання
12. **Очистити вимірювання**: Кнопка "🗑️ Clear" видаляє всі лінійки
13. **Координати миші**: Спостерігайте точні координати (4 знаки після коми)

### Навігація
14. **Перетягування мишею**: Навігація по великих схемах
15. **Reset View**: Повернення до початкового стану

## Ключові покращення

### Масштабування
- **Необмежений масштаб**: Від 0.01x до 1000x для детального перегляду мікро-елементів
- **Адаптивна товщина ліній**: Лінії автоматично стають тоншими при збільшенні масштабу
- **Адаптивні розміри**: Розміри міток та точок підлаштовуються під поточний масштаб
- **Автоматичний масштаб**: Геометрія автоматично масштабується для оптимального відображення
- **Fit to Window**: Кнопка для автоматичного підбору масштабу під розмір вікна

### Інтерфейс
- **Окреме поле координат**: Координати миші відображаються в реальному часі
- **Перемикач анотацій**: Можливість вимкнути мітки для кращого перегляду геометрії
- **Універсальність**: Працює з будь-якими точками, не прив'язаний до концепції "outlets"

### Навігація
- **Перетягування мишею**: Плавна навігація по великих схемах
- **Масштабування колесом**: Швидке збільшення/зменшення (експоненціальне)
- **Ручне введення масштабу**: Точний контроль через текстове поле
- **Кнопки +/-**: Покрокове масштабування
- **Reset View**: Повернення до початкового стану

### Інструмент лінійка
- **Активація**: Кнопка "📏 Measure" (перемикає режим вимірювання)
- **Використання**: Клікніть дві точки для створення лінійки
- **Інформація**: Показує довжину, зміщення по X та Y
- **Очищення**: Кнопка "🗑️ Clear" видаляє всі лінійки
- **Множинні вимірювання**: Можна створити декілька лінійок одночасно

## Формат JSON

Застосунок підтримує два формати для зворотної сумісності:

### Новий формат (рекомендований)
```json
{
  "Lines": [
    {
      "Coordinates": [
        {"X": 100.0, "Y": 200.0},
        {"X": 150.0, "Y": 250.0}
      ],
      "Length": 70.71
    }
  ],
  "Points": [
    {
      "Id": 1,
      "X": 100.0,
      "Y": 200.0
    }
  ]
}
```

### Старий формат (підтримується)
```json
{
  "Lines": [...],
  "Outlets": [
    {
      "Id": 1,
      "X": 100.0,
      "Y": 200.0
    }
  ]
}
```

## Запуск

- Через Visual Studio: відкрийте GeometryVisualizer.sln і запустіть
- Через командний рядок: `dotnet run --project GeometryVisualizer`
- Через батч-файл: запустіть `run_visualizer.bat` з кореневої папки проекту
