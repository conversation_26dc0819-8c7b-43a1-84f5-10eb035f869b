# Geometry Visualizer

WPF застосунок для візуалізації геометрії з JSON файлів, створених основним проектом PathwayLowVoltagePlan.

## Функціональність

- Завантаження JSON файлів з геометрією
- Візуалізація ліній з різними кольорами та адаптивною товщиною
- Відображення точок як жовті кружечки
- **Необмежене масштабування** (від 0.01x до 1000x) для детального перегляду
- Прокрутка та перетягування мишею для навігації
- Скидання виду (Reset View)
- Автоматичне підгонка під розмір вікна (Fit to Window)
- **Окреме поле для координат миші** в реальному часі
- **Перемикач відображення анотацій** (Show Labels)
- Адаптивні розміри елементів залежно від масштабу
- Незалежність від концепції "outlets" - працює з будь-якими точками

## Як використовувати

1. Запустіть основний проект PathwayLowVoltagePlan для створення geometry.json
2. Запустіть GeometryVisualizer
3. Вкажіть шлях до JSON файлу або використайте кнопку "Browse..."
4. Натисніть "Load" для завантаження геометрії
5. **Використайте слайдер Zoom (0.01x - 1000x) або колесо миші** для масштабування
6. **Вимкніть "Show Labels"** для кращого перегляду геометрії без анотацій
7. Використайте "Fit to Window" для автоматичного підбору масштабу
8. Перетягуйте мишею для навігації по схемі
9. **Спостерігайте координати миші** в окремому полі праворуч
10. Використайте "Reset View" для повернення до початкового виду

## Ключові покращення

### Масштабування
- **Необмежений масштаб**: Від 0.01x до 1000x для детального перегляду мікро-елементів
- **Адаптивна товщина ліній**: Лінії автоматично стають тоншими при збільшенні масштабу
- **Адаптивні розміри**: Розміри міток та точок підлаштовуються під поточний масштаб
- **Автоматичний масштаб**: Геометрія автоматично масштабується для оптимального відображення
- **Fit to Window**: Кнопка для автоматичного підбору масштабу під розмір вікна

### Інтерфейс
- **Окреме поле координат**: Координати миші відображаються в реальному часі
- **Перемикач анотацій**: Можливість вимкнути мітки для кращого перегляду геометрії
- **Універсальність**: Працює з будь-якими точками, не прив'язаний до концепції "outlets"

### Навігація
- **Перетягування мишею**: Плавна навігація по великих схемах
- **Масштабування колесом**: Швидке збільшення/зменшення
- **Reset View**: Повернення до початкового стану

## Формат JSON

Застосунок підтримує два формати для зворотної сумісності:

### Новий формат (рекомендований)
```json
{
  "Lines": [
    {
      "Coordinates": [
        {"X": 100.0, "Y": 200.0},
        {"X": 150.0, "Y": 250.0}
      ],
      "Length": 70.71
    }
  ],
  "Points": [
    {
      "Id": 1,
      "X": 100.0,
      "Y": 200.0
    }
  ]
}
```

### Старий формат (підтримується)
```json
{
  "Lines": [...],
  "Outlets": [
    {
      "Id": 1,
      "X": 100.0,
      "Y": 200.0
    }
  ]
}
```

## Запуск

- Через Visual Studio: відкрийте GeometryVisualizer.sln і запустіть
- Через командний рядок: `dotnet run --project GeometryVisualizer`
- Через батч-файл: запустіть `run_visualizer.bat` з кореневої папки проекту
