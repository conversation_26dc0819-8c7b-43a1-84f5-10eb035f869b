# Geometry Visualizer

WPF застосунок для візуалізації геометрії з JSON файлів, створених основним проектом PathwayLowVoltagePlan.

## Функціональність

- Завантаження JSON файлів з геометрією
- Візуалізація ліній з різними кольорами та адаптивною товщиною
- Відображення розеток (outlets) як жовті кружечки
- Масштабування (zoom) від 0.1x до 10x з автоматичним підбором розмірів
- Прокрутка та перетягування мишею для навігації
- Скидання виду (Reset View)
- Автоматичне підгонка під розмір вікна (Fit to Window)
- Відображення координат при наведенні миші
- Адаптивні розміри елементів залежно від масштабу

## Як використовувати

1. Запустіть основний проект PathwayLowVoltagePlan для створення geometry.json
2. Запустіть GeometryVisualizer
3. Вкажіть шлях до JSON файлу або використайте кнопку "Browse..."
4. Натисніть "Load" для завантаження геометрії
5. Використовуйте слайдер Zoom або колесо миші для масштабування
6. Використайте "Fit to Window" для автоматичного підбору масштабу
7. Перетягуйте мишею для навігації по схемі
8. Наводьте мишу для перегляду координат
9. Використайте "Reset View" для повернення до початкового виду

## Покращення масштабування

- **Адаптивна товщина ліній**: Лінії автоматично стають тоншими при збільшенні масштабу
- **Адаптивні розміри**: Розміри міток та розеток підлаштовуються під поточний масштаб
- **Автоматичний масштаб**: Геометрія автоматично масштабується для оптимального відображення
- **Fit to Window**: Кнопка для автоматичного підбору масштабу під розмір вікна

## Формат JSON

```json
{
  "Lines": [
    {
      "Coordinates": [
        {"X": 100.0, "Y": 200.0},
        {"X": 150.0, "Y": 250.0}
      ],
      "Length": 70.71
    }
  ],
  "Outlets": [
    {
      "Id": 1,
      "X": 100.0,
      "Y": 200.0
    }
  ]
}
```

## Запуск

- Через Visual Studio: відкрийте GeometryVisualizer.sln і запустіть
- Через командний рядок: `dotnet run --project GeometryVisualizer`
- Через батч-файл: запустіть `run_visualizer.bat` з кореневої папки проекту
