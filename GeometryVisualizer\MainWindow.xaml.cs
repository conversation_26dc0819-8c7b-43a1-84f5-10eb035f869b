using Microsoft.Win32;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;

namespace GeometryVisualizer
{
    public partial class MainWindow : Window
    {
        private GeometryData? _currentGeometry;
        private double _currentZoom = 1.0;
        private bool _isDragging = false;
        private Point _lastMousePosition;

        public MainWindow()
        {
            InitializeComponent();
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                Title = "Select geometry JSON file"
            };

            if (dialog.ShowDialog() == true)
            {
                FilePathTextBox.Text = dialog.FileName;
            }
        }

        private void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = FilePathTextBox.Text;
                if (!File.Exists(filePath))
                {
                    MessageBox.Show("File not found!", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var json = File.ReadAllText(filePath);
                _currentGeometry = JsonSerializer.Deserialize<GeometryData>(json);
                
                if (_currentGeometry != null)
                {
                    DrawGeometry();
                    StatusText.Text = $"Loaded {_currentGeometry.Lines.Length} lines, {_currentGeometry.Outlets.Length} outlets";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DrawGeometry()
        {
            if (_currentGeometry == null) return;

            GeometryCanvas.Children.Clear();

            // Check if we have any data to draw
            if (_currentGeometry.Lines.Length == 0 && _currentGeometry.Outlets.Length == 0)
            {
                StatusText.Text = "No geometry data to display";
                return;
            }

            // Calculate bounds
            var allX = new List<double>();
            var allY = new List<double>();

            // Add line coordinates
            foreach (var line in _currentGeometry.Lines)
            {
                foreach (var coord in line.Coordinates)
                {
                    allX.Add(coord.X);
                    allY.Add(coord.Y);
                }
            }

            // Add outlet coordinates
            foreach (var outlet in _currentGeometry.Outlets)
            {
                allX.Add(outlet.X);
                allY.Add(outlet.Y);
            }

            if (allX.Count == 0)
            {
                StatusText.Text = "No valid coordinates found";
                return;
            }

            var minX = allX.Min();
            var maxX = allX.Max();
            var minY = allY.Min();
            var maxY = allY.Max();

            var width = maxX - minX;
            var height = maxY - minY;

            // Ensure minimum size
            if (width < 1) width = 100;
            if (height < 1) height = 100;

            // Calculate scale factor to fit geometry nicely in the viewport
            var viewportWidth = CanvasScrollViewer.ActualWidth > 0 ? CanvasScrollViewer.ActualWidth : 800;
            var viewportHeight = CanvasScrollViewer.ActualHeight > 0 ? CanvasScrollViewer.ActualHeight : 600;

            var scaleX = (viewportWidth - 100) / width;  // 100px padding
            var scaleY = (viewportHeight - 100) / height;
            var autoScale = Math.Min(scaleX, scaleY) * 0.8; // 80% of available space

            // Apply auto-scale if it's reasonable, otherwise use current zoom
            var effectiveScale = autoScale > 0.1 && autoScale < 10 ? autoScale * _currentZoom : _currentZoom;

            var padding = 50 * effectiveScale;

            // Set canvas size
            GeometryCanvas.Width = (width * effectiveScale) + (padding * 2);
            GeometryCanvas.Height = (height * effectiveScale) + (padding * 2);

            // Draw lines
            var colors = new[] { Brushes.Red, Brushes.Blue, Brushes.Green, Brushes.Orange, Brushes.Purple, Brushes.Brown, Brushes.DarkCyan, Brushes.Magenta };
            for (int i = 0; i < _currentGeometry.Lines.Length; i++)
            {
                var line = _currentGeometry.Lines[i];
                var color = colors[i % colors.Length];

                if (line.Coordinates.Length < 2) continue; // Skip invalid lines

                for (int j = 0; j < line.Coordinates.Length - 1; j++)
                {
                    var start = line.Coordinates[j];
                    var end = line.Coordinates[j + 1];

                    var wpfLine = new Line
                    {
                        X1 = (start.X - minX) * effectiveScale + padding,
                        Y1 = (maxY - start.Y) * effectiveScale + padding, // Flip Y
                        X2 = (end.X - minX) * effectiveScale + padding,
                        Y2 = (maxY - end.Y) * effectiveScale + padding,   // Flip Y
                        Stroke = color,
                        StrokeThickness = Math.Max(0.5, Math.Min(3, effectiveScale * 0.1)) // Тонші лінії
                    };

                    GeometryCanvas.Children.Add(wpfLine);
                }

                // Add line label at the start of the line
                if (line.Coordinates.Length > 0)
                {
                    var firstCoord = line.Coordinates[0];
                    var fontSize = Math.Max(6, Math.Min(14, effectiveScale * 0.05)); // Адаптивний розмір шрифту
                    var label = new TextBlock
                    {
                        Text = $"L{i} ({line.Length:F1})",
                        Foreground = color,
                        FontSize = fontSize,
                        FontWeight = FontWeights.Bold,
                        Background = Brushes.White,
                        Opacity = 0.9
                    };
                    Canvas.SetLeft(label, (firstCoord.X - minX) * effectiveScale + padding + 3);
                    Canvas.SetTop(label, (maxY - firstCoord.Y) * effectiveScale + padding - fontSize - 2);
                    GeometryCanvas.Children.Add(label);
                }
            }

            // Draw outlets
            foreach (var outlet in _currentGeometry.Outlets)
            {
                var circleSize = Math.Max(4, Math.Min(12, effectiveScale * 0.03)); // Адаптивний розмір
                var circle = new Ellipse
                {
                    Width = circleSize,
                    Height = circleSize,
                    Fill = Brushes.Yellow,
                    Stroke = Brushes.Black,
                    StrokeThickness = Math.Max(0.5, effectiveScale * 0.005)
                };

                Canvas.SetLeft(circle, (outlet.X - minX) * effectiveScale + padding - circleSize / 2);
                Canvas.SetTop(circle, (maxY - outlet.Y) * effectiveScale + padding - circleSize / 2);
                GeometryCanvas.Children.Add(circle);

                var fontSize = Math.Max(6, Math.Min(12, effectiveScale * 0.04));
                var outletLabel = new TextBlock
                {
                    Text = $"O{outlet.Id}",
                    FontSize = fontSize,
                    Foreground = Brushes.Black,
                    FontWeight = FontWeights.Bold,
                    Background = Brushes.White,
                    Opacity = 0.9
                };
                Canvas.SetLeft(outletLabel, (outlet.X - minX) * effectiveScale + padding + circleSize / 2 + 2);
                Canvas.SetTop(outletLabel, (maxY - outlet.Y) * effectiveScale + padding - circleSize / 2 - 2);
                GeometryCanvas.Children.Add(outletLabel);
            }
        }

        private void ZoomSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            _currentZoom = e.NewValue;
                
            if (_currentGeometry != null)
            {
                DrawGeometry();
            }
        }

        private void Canvas_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            var delta = e.Delta > 0 ? 0.1 : -0.1;
            var newZoom = Math.Max(0.1, Math.Min(10, _currentZoom + delta));
            ZoomSlider.Value = newZoom;
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomSlider.Value = 1.0;
            CanvasScrollViewer.ScrollToHorizontalOffset(0);
            CanvasScrollViewer.ScrollToVerticalOffset(0);
        }

        private void FitToWindowButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentGeometry == null) return;

            // Calculate optimal zoom to fit all geometry in the viewport
            var allX = new List<double>();
            var allY = new List<double>();

            foreach (var line in _currentGeometry.Lines)
            {
                foreach (var coord in line.Coordinates)
                {
                    allX.Add(coord.X);
                    allY.Add(coord.Y);
                }
            }

            foreach (var outlet in _currentGeometry.Outlets)
            {
                allX.Add(outlet.X);
                allY.Add(outlet.Y);
            }

            if (allX.Count == 0) return;

            var minX = allX.Min();
            var maxX = allX.Max();
            var minY = allY.Min();
            var maxY = allY.Max();

            var width = maxX - minX;
            var height = maxY - minY;

            if (width < 1) width = 100;
            if (height < 1) height = 100;

            var viewportWidth = CanvasScrollViewer.ActualWidth > 0 ? CanvasScrollViewer.ActualWidth : 800;
            var viewportHeight = CanvasScrollViewer.ActualHeight > 0 ? CanvasScrollViewer.ActualHeight : 600;

            var scaleX = (viewportWidth - 100) / width;
            var scaleY = (viewportHeight - 100) / height;
            var optimalZoom = Math.Min(scaleX, scaleY) * 0.9; // 90% of available space

            // Clamp zoom to slider range
            optimalZoom = Math.Max(ZoomSlider.Minimum, Math.Min(ZoomSlider.Maximum, optimalZoom));

            ZoomSlider.Value = optimalZoom;

            // Center the view
            CanvasScrollViewer.ScrollToHorizontalOffset(0);
            CanvasScrollViewer.ScrollToVerticalOffset(0);
        }

        private void Canvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _isDragging = true;
            _lastMousePosition = e.GetPosition(CanvasScrollViewer);
            GeometryCanvas.CaptureMouse();
        }

        private void Canvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            _isDragging = false;
            GeometryCanvas.ReleaseMouseCapture();
        }

        private void Canvas_MouseMove(object sender, MouseEventArgs e)
        {
            // Update coordinates display
            if (_currentGeometry != null)
            {
                var mousePos = e.GetPosition(GeometryCanvas);

                // Convert canvas coordinates back to geometry coordinates
                var allX = new List<double>();
                var allY = new List<double>();

                foreach (var line in _currentGeometry.Lines)
                {
                    foreach (var coord in line.Coordinates)
                    {
                        allX.Add(coord.X);
                        allY.Add(coord.Y);
                    }
                }

                foreach (var outlet in _currentGeometry.Outlets)
                {
                    allX.Add(outlet.X);
                    allY.Add(outlet.Y);
                }

                if (allX.Count > 0)
                {
                    var minX = allX.Min();
                    var maxX = allX.Max();
                    var minY = allY.Min();
                    var maxY = allY.Max();

                    var width = maxX - minX;
                    var height = maxY - minY;
                    if (width < 1) width = 100;
                    if (height < 1) height = 100;

                    var viewportWidth = CanvasScrollViewer.ActualWidth > 0 ? CanvasScrollViewer.ActualWidth : 800;
                    var viewportHeight = CanvasScrollViewer.ActualHeight > 0 ? CanvasScrollViewer.ActualHeight : 600;

                    var scaleX = (viewportWidth - 100) / width;
                    var scaleY = (viewportHeight - 100) / height;
                    var autoScale = Math.Min(scaleX, scaleY) * 0.8;
                    var effectiveScale = autoScale > 0.1 && autoScale < 10 ? autoScale * _currentZoom : _currentZoom;
                    var padding = 50 * effectiveScale;

                    var geoX = (mousePos.X - padding) / effectiveScale + minX;
                    var geoY = maxY - (mousePos.Y - padding) / effectiveScale;

                    CoordinatesText.Text = $"X: {geoX:F2}, Y: {geoY:F2}";
                }
            }

            if (_isDragging)
            {
                var currentPosition = e.GetPosition(CanvasScrollViewer);
                var deltaX = currentPosition.X - _lastMousePosition.X;
                var deltaY = currentPosition.Y - _lastMousePosition.Y;

                CanvasScrollViewer.ScrollToHorizontalOffset(CanvasScrollViewer.HorizontalOffset - deltaX);
                CanvasScrollViewer.ScrollToVerticalOffset(CanvasScrollViewer.VerticalOffset - deltaY);

                _lastMousePosition = currentPosition;
            }
        }
    }

    public class GeometryData
    {
        public LineData[] Lines { get; set; } = Array.Empty<LineData>();
        public OutletData[] Outlets { get; set; } = Array.Empty<OutletData>();
    }

    public class LineData
    {
        public CoordinateData[] Coordinates { get; set; } = Array.Empty<CoordinateData>();
        public double Length { get; set; }
    }

    public class OutletData
    {
        public int Id { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
    }

    public class CoordinateData
    {
        public double X { get; set; }
        public double Y { get; set; }
    }
}