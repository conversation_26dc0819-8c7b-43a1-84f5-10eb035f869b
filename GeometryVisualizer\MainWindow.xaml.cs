using Microsoft.Win32;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;

namespace GeometryVisualizer
{
    public partial class MainWindow : Window
    {
        private GeometryData? _currentGeometry;
        private double _currentZoom = 1.0;
        private bool _isDragging = false;
        private Point _lastMousePosition;
        private bool _showAnnotations = true;
        private bool _measureMode = false;
        private Point? _measureStartPoint = null;
        private List<MeasureLine> _measureLines = new List<MeasureLine>();
        private bool _updatingZoom = false;

        public MainWindow()
        {
            InitializeComponent();
            UpdateZoomDisplay(); // Ініціалізуємо відображення зуму після InitializeComponent
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                Title = "Select geometry JSON file"
            };

            if (dialog.ShowDialog() == true)
            {
                FilePathTextBox.Text = dialog.FileName;
            }
        }

        private void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = FilePathTextBox.Text;
                if (!File.Exists(filePath))
                {
                    MessageBox.Show("File not found!", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var json = File.ReadAllText(filePath);
                _currentGeometry = JsonSerializer.Deserialize<GeometryData>(json);
                
                if (_currentGeometry != null)
                {
                    DrawGeometry();
                    StatusText.Text = $"Loaded {_currentGeometry.Lines.Length} lines, {_currentGeometry.Points.Length} points";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DrawGeometry()
        {
            if (_currentGeometry == null) return;

            GeometryCanvas.Children.Clear();

            // Check if we have any data to draw
            if (_currentGeometry.Lines.Length == 0 && _currentGeometry.Points.Length == 0)
            {
                StatusText.Text = "No geometry data to display";
                return;
            }

            // Calculate bounds
            var allX = new List<double>();
            var allY = new List<double>();

            // Add line coordinates
            foreach (var line in _currentGeometry.Lines)
            {
                foreach (var coord in line.Coordinates)
                {
                    allX.Add(coord.X);
                    allY.Add(coord.Y);
                }
            }

            // Add point coordinates
            foreach (var point in _currentGeometry.Points)
            {
                allX.Add(point.X);
                allY.Add(point.Y);
            }

            if (allX.Count == 0)
            {
                StatusText.Text = "No valid coordinates found";
                return;
            }

            var minX = allX.Min();
            var maxX = allX.Max();
            var minY = allY.Min();
            var maxY = allY.Max();

            var width = maxX - minX;
            var height = maxY - minY;

            // Ensure minimum size
            if (width < 1) width = 100;
            if (height < 1) height = 100;

            // Calculate scale factor to fit geometry nicely in the viewport
            var viewportWidth = CanvasScrollViewer.ActualWidth > 0 ? CanvasScrollViewer.ActualWidth : 800;
            var viewportHeight = CanvasScrollViewer.ActualHeight > 0 ? CanvasScrollViewer.ActualHeight : 600;

            var scaleX = (viewportWidth - 100) / width;  // 100px padding
            var scaleY = (viewportHeight - 100) / height;
            var autoScale = Math.Min(scaleX, scaleY) * 0.8; // 80% of available space

            // Apply auto-scale if it's reasonable, otherwise use current zoom
            var effectiveScale = autoScale > 0.1 && autoScale < 10 ? autoScale * _currentZoom : _currentZoom;

            var padding = 50 * effectiveScale;

            // Set canvas size
            GeometryCanvas.Width = (width * effectiveScale) + (padding * 2);
            GeometryCanvas.Height = (height * effectiveScale) + (padding * 2);

            // Draw lines
            var colors = new[] { Brushes.Red, Brushes.Blue, Brushes.Green, Brushes.Orange, Brushes.Purple, Brushes.Brown, Brushes.DarkCyan, Brushes.Magenta };
            for (int i = 0; i < _currentGeometry.Lines.Length; i++)
            {
                var line = _currentGeometry.Lines[i];
                var color = colors[i % colors.Length];

                if (line.Coordinates.Length < 2) continue; // Skip invalid lines

                for (int j = 0; j < line.Coordinates.Length - 1; j++)
                {
                    var start = line.Coordinates[j];
                    var end = line.Coordinates[j + 1];

                    var wpfLine = new Line
                    {
                        X1 = (start.X - minX) * effectiveScale + padding,
                        Y1 = (maxY - start.Y) * effectiveScale + padding, // Flip Y
                        X2 = (end.X - minX) * effectiveScale + padding,
                        Y2 = (maxY - end.Y) * effectiveScale + padding,   // Flip Y
                        Stroke = color,
                        StrokeThickness = Math.Max(0.5, Math.Min(3, effectiveScale * 0.1)) // Тонші лінії
                    };

                    GeometryCanvas.Children.Add(wpfLine);
                }

                // Add coordinate annotations for all line points (if annotations are enabled)
                if (_showAnnotations)
                {
                    var fontSize = Math.Max(8, Math.Min(18, effectiveScale * 0.08)); // Збільшений розмір в 2-3 рази

                    for (int j = 0; j < line.Coordinates.Length; j++)
                    {
                        var coord = line.Coordinates[j];
                        var label = new TextBlock
                        {
                            Text = $"({coord.X:F4}, {coord.Y:F4})",
                            Foreground = Brushes.DarkBlue,
                            FontSize = fontSize,
                            FontWeight = FontWeights.Bold,
                            Background = Brushes.White,
                            Opacity = 0.9
                        };

                        // Offset to avoid overlapping
                        var offsetX = (j % 2 == 0) ? 5 : -60;
                        var offsetY = (j % 2 == 0) ? -fontSize - 2 : fontSize + 2;

                        Canvas.SetLeft(label, (coord.X - minX) * effectiveScale + padding + offsetX);
                        Canvas.SetTop(label, (maxY - coord.Y) * effectiveScale + padding + offsetY);
                        GeometryCanvas.Children.Add(label);
                    }
                }
            }

            // Draw points
            foreach (var point in _currentGeometry.Points)
            {
                var circleSize = Math.Max(4, Math.Min(12, effectiveScale * 0.03)); // Адаптивний розмір
                var circle = new Ellipse
                {
                    Width = circleSize,
                    Height = circleSize,
                    Fill = Brushes.Yellow,
                    Stroke = Brushes.Black,
                    StrokeThickness = Math.Max(0.5, effectiveScale * 0.005)
                };

                Canvas.SetLeft(circle, (point.X - minX) * effectiveScale + padding - circleSize / 2);
                Canvas.SetTop(circle, (maxY - point.Y) * effectiveScale + padding - circleSize / 2);
                GeometryCanvas.Children.Add(circle);

                // Add point coordinate label (if annotations are enabled)
                if (_showAnnotations)
                {
                    var fontSize = Math.Max(8, Math.Min(18, effectiveScale * 0.08)); // Збільшений розмір
                    var pointLabel = new TextBlock
                    {
                        Text = $"P{point.Id}: ({point.X:F4}, {point.Y:F4})",
                        FontSize = fontSize,
                        Foreground = Brushes.DarkRed,
                        FontWeight = FontWeights.Bold,
                        Background = Brushes.White,
                        Opacity = 0.9
                    };
                    Canvas.SetLeft(pointLabel, (point.X - minX) * effectiveScale + padding + circleSize / 2 + 2);
                    Canvas.SetTop(pointLabel, (maxY - point.Y) * effectiveScale + padding - circleSize / 2 - fontSize - 2);
                    GeometryCanvas.Children.Add(pointLabel);
                }
            }

            // Draw measure lines
            foreach (var measureLine in _measureLines)
            {
                var startCanvas = GeometryToCanvasCoordinates(measureLine.Start, minX, maxY, effectiveScale, padding);
                var endCanvas = GeometryToCanvasCoordinates(measureLine.End, minX, maxY, effectiveScale, padding);

                // Draw measure line
                var line = new Line
                {
                    X1 = startCanvas.X,
                    Y1 = startCanvas.Y,
                    X2 = endCanvas.X,
                    Y2 = endCanvas.Y,
                    Stroke = Brushes.Red,
                    StrokeThickness = Math.Max(1, 2 * effectiveScale * 0.01),
                    StrokeDashArray = new DoubleCollection { 5, 5 }
                };
                GeometryCanvas.Children.Add(line);

                // Draw measure annotation at center
                var centerX = (startCanvas.X + endCanvas.X) / 2;
                var centerY = (startCanvas.Y + endCanvas.Y) / 2;
                var fontSize = Math.Max(10, Math.Min(20, effectiveScale * 0.1));

                var measureLabel = new TextBlock
                {
                    Text = $"L: {measureLine.Length:F4}\nΔX: {measureLine.DeltaX:F4}\nΔY: {measureLine.DeltaY:F4}",
                    FontSize = fontSize,
                    Foreground = Brushes.Red,
                    FontWeight = FontWeights.Bold,
                    Background = Brushes.White,
                    Opacity = 0.95,
                    TextAlignment = TextAlignment.Center
                };
                Canvas.SetLeft(measureLabel, centerX - 40);
                Canvas.SetTop(measureLabel, centerY - fontSize * 1.5);
                GeometryCanvas.Children.Add(measureLabel);
            }
        }

        private Point GeometryToCanvasCoordinates(Point geoPoint, double minX, double maxY, double effectiveScale, double padding)
        {
            var canvasX = (geoPoint.X - minX) * effectiveScale + padding;
            var canvasY = (maxY - geoPoint.Y) * effectiveScale + padding;
            return new Point(canvasX, canvasY);
        }

        private void ZoomSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_updatingZoom || ZoomLabel == null) return;

            _currentZoom = e.NewValue;
            UpdateZoomDisplay();
            if (_currentGeometry != null)
            {
                DrawGeometry();
            }
        }

        private void UpdateZoomDisplay()
        {
            if (ZoomLabel == null || ZoomTextBox == null || ZoomSlider == null) return;

            _updatingZoom = true;
            ZoomLabel.Text = $"{_currentZoom:F2}x";
            ZoomTextBox.Text = _currentZoom.ToString("F2");
            ZoomSlider.Value = _currentZoom;
            _updatingZoom = false;
        }

        private void Canvas_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            e.Handled = true; // Prevent scrolling

            var delta = e.Delta > 0 ? 0.3 : -0.3; // 3x faster zoom
            var factor = e.Delta > 0 ? 1.3 : 1.0 / 1.3; // Exponential zoom
            var newZoom = Math.Max(0.01, Math.Min(1000, _currentZoom * factor));

            _currentZoom = newZoom;
            UpdateZoomDisplay();

            if (_currentGeometry != null)
            {
                DrawGeometry();
            }
        }

        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            _currentZoom = Math.Min(1000, _currentZoom * 1.5);
            UpdateZoomDisplay();
            if (_currentGeometry != null) DrawGeometry();
        }

        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            _currentZoom = Math.Max(0.01, _currentZoom / 1.5);
            UpdateZoomDisplay();
            if (_currentGeometry != null) DrawGeometry();
        }

        private void ZoomTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (double.TryParse(ZoomTextBox.Text, out double zoom))
                {
                    _currentZoom = Math.Max(0.01, Math.Min(1000, zoom));
                    UpdateZoomDisplay();
                    if (_currentGeometry != null) DrawGeometry();
                }
                else
                {
                    UpdateZoomDisplay(); // Reset to current value
                }
            }
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            _currentZoom = 1.0;
            UpdateZoomDisplay();
            CanvasScrollViewer.ScrollToHorizontalOffset(0);
            CanvasScrollViewer.ScrollToVerticalOffset(0);
            if (_currentGeometry != null) DrawGeometry();
        }

        private void MeasureButton_Click(object sender, RoutedEventArgs e)
        {
            _measureMode = !_measureMode;
            if (MeasureButton != null)
            {
                MeasureButton.Content = _measureMode ? "📏 Stop" : "📏 Measure";
            }
            if (GeometryCanvas != null)
            {
                GeometryCanvas.Cursor = _measureMode ? Cursors.Cross : Cursors.Arrow;
            }
            _measureStartPoint = null;
        }

        private void ClearMeasuresButton_Click(object sender, RoutedEventArgs e)
        {
            _measureLines.Clear();
            if (_currentGeometry != null)
            {
                DrawGeometry();
            }
        }

        private void ShowAnnotationsCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            _showAnnotations = ShowAnnotationsCheckBox.IsChecked == true;
            if (_currentGeometry != null)
            {
                DrawGeometry();
            }
        }

        private void FitToWindowButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentGeometry == null) return;

            // Calculate optimal zoom to fit all geometry in the viewport
            var allX = new List<double>();
            var allY = new List<double>();

            foreach (var line in _currentGeometry.Lines)
            {
                foreach (var coord in line.Coordinates)
                {
                    allX.Add(coord.X);
                    allY.Add(coord.Y);
                }
            }

            foreach (var point in _currentGeometry.Points)
            {
                allX.Add(point.X);
                allY.Add(point.Y);
            }

            if (allX.Count == 0) return;

            var minX = allX.Min();
            var maxX = allX.Max();
            var minY = allY.Min();
            var maxY = allY.Max();

            var width = maxX - minX;
            var height = maxY - minY;

            if (width < 1) width = 100;
            if (height < 1) height = 100;

            var viewportWidth = CanvasScrollViewer.ActualWidth > 0 ? CanvasScrollViewer.ActualWidth : 800;
            var viewportHeight = CanvasScrollViewer.ActualHeight > 0 ? CanvasScrollViewer.ActualHeight : 600;

            var scaleX = (viewportWidth - 100) / width;
            var scaleY = (viewportHeight - 100) / height;
            var optimalZoom = Math.Min(scaleX, scaleY) * 0.9; // 90% of available space

            // Clamp zoom to slider range
            optimalZoom = Math.Max(ZoomSlider.Minimum, Math.Min(ZoomSlider.Maximum, optimalZoom));

            ZoomSlider.Value = optimalZoom;

            // Center the view
            CanvasScrollViewer.ScrollToHorizontalOffset(0);
            CanvasScrollViewer.ScrollToVerticalOffset(0);
        }

        private void Canvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_measureMode)
            {
                var mousePos = e.GetPosition(GeometryCanvas);
                var geoPoint = CanvasToGeometryCoordinates(mousePos);

                if (_measureStartPoint == null)
                {
                    _measureStartPoint = geoPoint;
                }
                else
                {
                    var measureLine = new MeasureLine
                    {
                        Start = _measureStartPoint.Value,
                        End = geoPoint
                    };
                    _measureLines.Add(measureLine);
                    _measureStartPoint = null;
                    DrawGeometry();
                }
            }
            else
            {
                _isDragging = true;
                _lastMousePosition = e.GetPosition(CanvasScrollViewer);
                GeometryCanvas.CaptureMouse();
            }
        }

        private void Canvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (!_measureMode)
            {
                _isDragging = false;
                GeometryCanvas.ReleaseMouseCapture();
            }
        }

        private void Canvas_MouseMove(object sender, MouseEventArgs e)
        {
            // Update coordinates display
            if (_currentGeometry != null && MouseCoordinatesText != null)
            {
                var mousePos = e.GetPosition(GeometryCanvas);

                // Convert canvas coordinates back to geometry coordinates
                var allX = new List<double>();
                var allY = new List<double>();

                foreach (var line in _currentGeometry.Lines)
                {
                    foreach (var coord in line.Coordinates)
                    {
                        allX.Add(coord.X);
                        allY.Add(coord.Y);
                    }
                }

                foreach (var point in _currentGeometry.Points)
                {
                    allX.Add(point.X);
                    allY.Add(point.Y);
                }

                if (allX.Count > 0)
                {
                    var minX = allX.Min();
                    var maxX = allX.Max();
                    var minY = allY.Min();
                    var maxY = allY.Max();

                    var width = maxX - minX;
                    var height = maxY - minY;
                    if (width < 1) width = 100;
                    if (height < 1) height = 100;

                    var viewportWidth = CanvasScrollViewer.ActualWidth > 0 ? CanvasScrollViewer.ActualWidth : 800;
                    var viewportHeight = CanvasScrollViewer.ActualHeight > 0 ? CanvasScrollViewer.ActualHeight : 600;

                    var scaleX = (viewportWidth - 100) / width;
                    var scaleY = (viewportHeight - 100) / height;
                    var autoScale = Math.Min(scaleX, scaleY) * 0.8;
                    var effectiveScale = autoScale > 0.1 && autoScale < 10 ? autoScale * _currentZoom : _currentZoom;
                    var padding = 50 * effectiveScale;

                    var geoX = (mousePos.X - padding) / effectiveScale + minX;
                    var geoY = maxY - (mousePos.Y - padding) / effectiveScale;

                    MouseCoordinatesText.Text = $"X: {geoX:F4}, Y: {geoY:F4}";
                }
            }
            else if (MouseCoordinatesText != null)
            {
                MouseCoordinatesText.Text = "X: 0.0000, Y: 0.0000";
            }

            if (_isDragging)
            {
                var currentPosition = e.GetPosition(CanvasScrollViewer);
                var deltaX = currentPosition.X - _lastMousePosition.X;
                var deltaY = currentPosition.Y - _lastMousePosition.Y;

                CanvasScrollViewer.ScrollToHorizontalOffset(CanvasScrollViewer.HorizontalOffset - deltaX);
                CanvasScrollViewer.ScrollToVerticalOffset(CanvasScrollViewer.VerticalOffset - deltaY);

                _lastMousePosition = currentPosition;
            }
        }

        private Point CanvasToGeometryCoordinates(Point canvasPoint)
        {
            if (_currentGeometry == null) return new Point(0, 0);

            var allX = new List<double>();
            var allY = new List<double>();

            foreach (var line in _currentGeometry.Lines)
            {
                foreach (var coord in line.Coordinates)
                {
                    allX.Add(coord.X);
                    allY.Add(coord.Y);
                }
            }

            foreach (var point in _currentGeometry.Points)
            {
                allX.Add(point.X);
                allY.Add(point.Y);
            }

            if (allX.Count == 0) return new Point(0, 0);

            var minX = allX.Min();
            var maxX = allX.Max();
            var minY = allY.Min();
            var maxY = allY.Max();

            var width = maxX - minX;
            var height = maxY - minY;
            if (width < 1) width = 100;
            if (height < 1) height = 100;

            var viewportWidth = CanvasScrollViewer.ActualWidth > 0 ? CanvasScrollViewer.ActualWidth : 800;
            var viewportHeight = CanvasScrollViewer.ActualHeight > 0 ? CanvasScrollViewer.ActualHeight : 600;

            var scaleX = (viewportWidth - 100) / width;
            var scaleY = (viewportHeight - 100) / height;
            var autoScale = Math.Min(scaleX, scaleY) * 0.8;
            var effectiveScale = autoScale > 0.1 && autoScale < 10 ? autoScale * _currentZoom : _currentZoom;
            var padding = 50 * effectiveScale;

            var geoX = (canvasPoint.X - padding) / effectiveScale + minX;
            var geoY = maxY - (canvasPoint.Y - padding) / effectiveScale;

            return new Point(geoX, geoY);
        }
    }

    public class MeasureLine
    {
        public Point Start { get; set; }
        public Point End { get; set; }

        public double Length => Math.Sqrt(Math.Pow(End.X - Start.X, 2) + Math.Pow(End.Y - Start.Y, 2));
        public double DeltaX => End.X - Start.X;
        public double DeltaY => End.Y - Start.Y;
    }

    public class GeometryData
    {
        public LineData[] Lines { get; set; } = Array.Empty<LineData>();
        public PointData[] Points { get; set; } = Array.Empty<PointData>();

        // Backward compatibility - map Outlets to Points
        public OutletData[] Outlets
        {
            get => Points.Select(p => new OutletData { Id = p.Id, X = p.X, Y = p.Y }).ToArray();
            set => Points = value.Select(o => new PointData { Id = o.Id, X = o.X, Y = o.Y }).ToArray();
        }
    }

    public class LineData
    {
        public CoordinateData[] Coordinates { get; set; } = Array.Empty<CoordinateData>();
        public double Length { get; set; }
    }

    public class PointData
    {
        public int Id { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
    }

    // Backward compatibility
    public class OutletData
    {
        public int Id { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
    }

    public class CoordinateData
    {
        public double X { get; set; }
        public double Y { get; set; }
    }
}