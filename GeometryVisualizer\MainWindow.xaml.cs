using Microsoft.Win32;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;

namespace GeometryVisualizer
{
    public partial class MainWindow : Window
    {
        private GeometryData? _currentGeometry;
        private double _currentZoom = 1.0;

        public MainWindow()
        {
            InitializeComponent();
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                Title = "Select geometry JSON file"
            };

            if (dialog.ShowDialog() == true)
            {
                FilePathTextBox.Text = dialog.FileName;
            }
        }

        private void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = FilePathTextBox.Text;
                if (!File.Exists(filePath))
                {
                    MessageBox.Show("File not found!", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var json = File.ReadAllText(filePath);
                _currentGeometry = JsonSerializer.Deserialize<GeometryData>(json);
                
                if (_currentGeometry != null)
                {
                    DrawGeometry();
                    StatusText.Text = $"Loaded {_currentGeometry.Lines.Length} lines, {_currentGeometry.Outlets.Length} outlets";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DrawGeometry()
        {
            if (_currentGeometry == null) return;

            GeometryCanvas.Children.Clear();

            // Calculate bounds
            var allX = _currentGeometry.Lines.SelectMany(l => l.Coordinates.Select(c => c.X))
                .Concat(_currentGeometry.Outlets.Select(o => o.X));
            var allY = _currentGeometry.Lines.SelectMany(l => l.Coordinates.Select(c => c.Y))
                .Concat(_currentGeometry.Outlets.Select(o => o.Y));

            var minX = allX.Min();
            var maxX = allX.Max();
            var minY = allY.Min();
            var maxY = allY.Max();

            var width = maxX - minX;
            var height = maxY - minY;
            var padding = 50;

            // Set canvas size
            GeometryCanvas.Width = (width + padding * 2) * _currentZoom;
            GeometryCanvas.Height = (height + padding * 2) * _currentZoom;

            // Draw lines
            var colors = new[] { Brushes.Red, Brushes.Blue, Brushes.Green, Brushes.Orange, Brushes.Purple, Brushes.Brown };
            for (int i = 0; i < _currentGeometry.Lines.Length; i++)
            {
                var line = _currentGeometry.Lines[i];
                var color = colors[i % colors.Length];

                for (int j = 0; j < line.Coordinates.Length - 1; j++)
                {
                    var start = line.Coordinates[j];
                    var end = line.Coordinates[j + 1];

                    var wpfLine = new Line
                    {
                        X1 = (start.X - minX + padding) * _currentZoom,
                        Y1 = (maxY - start.Y + padding) * _currentZoom, // Flip Y
                        X2 = (end.X - minX + padding) * _currentZoom,
                        Y2 = (maxY - end.Y + padding) * _currentZoom,   // Flip Y
                        Stroke = color,
                        StrokeThickness = 2 * _currentZoom
                    };

                    GeometryCanvas.Children.Add(wpfLine);
                }

                // Add line label
                var firstCoord = line.Coordinates[0];
                var label = new TextBlock
                {
                    Text = $"L{i}",
                    Foreground = color,
                    FontSize = 12 * _currentZoom,
                    FontWeight = FontWeights.Bold
                };
                Canvas.SetLeft(label, (firstCoord.X - minX + padding) * _currentZoom + 5);
                Canvas.SetTop(label, (maxY - firstCoord.Y + padding) * _currentZoom - 20);
                GeometryCanvas.Children.Add(label);
            }

            // Draw outlets
            foreach (var outlet in _currentGeometry.Outlets)
            {
                var circle = new Ellipse
                {
                    Width = 8 * _currentZoom,
                    Height = 8 * _currentZoom,
                    Fill = Brushes.Yellow,
                    Stroke = Brushes.Black,
                    StrokeThickness = 1 * _currentZoom
                };

                Canvas.SetLeft(circle, (outlet.X - minX + padding) * _currentZoom - 4 * _currentZoom);
                Canvas.SetTop(circle, (maxY - outlet.Y + padding) * _currentZoom - 4 * _currentZoom);
                GeometryCanvas.Children.Add(circle);

                var outletLabel = new TextBlock
                {
                    Text = outlet.Id.ToString(),
                    FontSize = 10 * _currentZoom,
                    Foreground = Brushes.Black
                };
                Canvas.SetLeft(outletLabel, (outlet.X - minX + padding) * _currentZoom + 6);
                Canvas.SetTop(outletLabel, (maxY - outlet.Y + padding) * _currentZoom - 15);
                GeometryCanvas.Children.Add(outletLabel);
            }
        }

        private void ZoomSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            _currentZoom = e.NewValue;
            ZoomLabel.Text = $"{_currentZoom:F1}x";
            if (_currentGeometry != null)
            {
                DrawGeometry();
            }
        }

        private void Canvas_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            var delta = e.Delta > 0 ? 0.1 : -0.1;
            var newZoom = Math.Max(0.1, Math.Min(10, _currentZoom + delta));
            ZoomSlider.Value = newZoom;
        }
    }

    public class GeometryData
    {
        public LineData[] Lines { get; set; } = Array.Empty<LineData>();
        public OutletData[] Outlets { get; set; } = Array.Empty<OutletData>();
    }

    public class LineData
    {
        public CoordinateData[] Coordinates { get; set; } = Array.Empty<CoordinateData>();
        public double Length { get; set; }
    }

    public class OutletData
    {
        public int Id { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
    }

    public class CoordinateData
    {
        public double X { get; set; }
        public double Y { get; set; }
    }
}