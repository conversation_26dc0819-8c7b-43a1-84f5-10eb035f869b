using NetTopologySuite.Geometries;
using PathwayLowVoltagePlan.Models;
using System.Text.Json;

namespace PathwayLowVoltagePlan.Services
{
    public class GeometryExportService
    {
        public void ExportToJson(List<LineString> lines, List<OutletModel> outlets, string filePath)
        {
            var data = new
            {
                Lines = lines.Select(line => new
                {
                    Coordinates = line.Coordinates.Select(c => new { X = c.X, Y = c.Y }).ToArray(),
                    Length = line.Length
                }).ToArray(),
                Outlets = outlets.Select(outlet => new
                {
                    Id = outlet.Id,
                    X = outlet.Location.X,
                    Y = outlet.Location.Y
                }).ToArray()
            };

            var json = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(filePath, json);
        }
    }
}