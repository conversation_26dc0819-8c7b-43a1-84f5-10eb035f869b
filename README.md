# PATHWAY LOW VOLTAGE PLAN TOOL
Інструмент для автоматизації проектування низьковольтних електропроводок в Revit.

### Ціль 
Автоматизувати створення електропроводки в Revit для проектувальників, моделюючи траси проводів як перила та додаючи анотації з кількістю проводів. Це треба щоб спростити процес розміщення проводів і анотацій, зменшити помилки через неточності ліній і автоматизувати рутинну роботу.

### Короткий Опис Бізнес Логіки 
Аналізуємо лінії, нормалізуємо їх, будуємо дерево трас, генеруємо перила, розміщуємо анотації та обчислюємо кількість проводів. Це ізольовано від Revit API через абстрактні моделі.

# Загальні Примітки
- Усі моделі є абстракціями, ізольованими від Revit API.
- Для роботи з геометрією використовується NetTopologySuite. Конвертація геометричних обєктів із Revit до NetTopologySuite відбувається поза бізнес логікую. Як і конвертація обєктів в Revit елементи.

# Моделі

## 1. OutletModel
Властивості:
int Id – Унікальний ідентифікатор розетки.
Point Location – Координати розташування розетки.
int WireCount – Кількість проводів, пов’язаних із розеткою.

## 2. RailingModel
Властивості:
int Id – Унікальний ідентифікатор елемента перил.
List<LineString> Lines – Список ліній, що визначають траєкторію перил (з NetTopologySuite).
int WireCount – Кількість проводів, асоційованих із перилами.
OutletModel SourceOutlet – Посилання на розетку, звідки йде траса.

## 3. AnnotationModel
Властивості:
int Id – Унікальний ідентифікатор анотації.
Point Location – Координати розміщення анотації.
int WireCount – Кількість проводів, вказана в анотації.
Direction Orientation – Напрямок анотації (Enum: Up, Down, Left, Right, None) для уникнення перетинів із трасою.

## 4. GeometryTreeModel
Властивості:
Point Root – Координати щитка (вільного кінця).
List<OutletModel> Leaves – Список розеток (листя дерева).
Dictionary<OutletModel, List<LineString>> Paths – Мапінг розеток до списків ліній, що утворюють трасу до щитка.

## 6. Direction (enum)
Up
Down
Left
Right

## Примітки
- Усі моделі є абстракціями, ізольованими від Revit API.

# Сервіси

## 1. LineNormalizationService
Відповідальність: Нормалізує вхідні лінії, усуваючи неточності (об’єднання ліній, обробка Т-подібних перетинів, видалення коротких ліній).
Константи:
const double TOLERANCE = 2.0 / 12.0 – Похибка 2" (≈ 0.16667 фути) для близькості точок.
Публічні методи:
List<LineString> NormalizeLines(List<LineString> rawLines)
Аргументи: rawLines – Список вхідних ліній типу NetTopologySuite.Geometries.LineString.
Повертає: Нормалізований список ліній LineString.
Логіка: Викликає приватні методи для мержу, спліту та видалення коротких ліній.
Приватні методи:
List<LineString> MergeLines(List<LineString> lines)
Аргументи: lines – Список ліній.
Логіка: Об’єднує лінії, що накладаються або продовжують одна одну в пряму, використовуючи TOLERANCE для визначення близькості.
List<LineString> FixUndershotLines(List<LineString> lines)
Аргументи: lines – Список ліній.
Логіка: Дотягує лінії до Т-подібних перетинів (не паралельних ліній), якщо геометричне перетинання відсутнє в межах TOLERANCE.
List<LineString> SplitLines(List<LineString> lines)
Аргументи: lines – Список ліній.
Логіка: Розділяє лінії в точках перетину (Х- і Т-подібних), використовуючи GeometryOverlay.Intersection.
List<LineString> RemoveShortLines(List<LineString> lines)
Аргументи: lines – Список ліній.
Логіка: Видаляє лінії, довжина яких менша за TOLERANCE.

## 2. GeometryAnalyzerService
Відповідальність: Аналізує нормалізовані лінії, будує дерево та визначає трасу від розеток до щитка.
Константи:
const double OUTLET_CONNECTION_TOLERANCE = 6.0 / 12.0 – Похибка 6" (≈ 0.5 фути) для з’єднання розеток із кінцями ліній.
Публічні методи:
GeometryTreeModel AnalyzeGeometry(List<LineString> normalizedLines, List<OutletModel> outlets)
Аргументи: normalizedLines – Нормалізовані лінії, outlets – Список розеток.
Повертає: GeometryTreeModel із побудованою структурою дерева.
Логіка: Визначає корінь і будує Paths для трас.
Приватні методи:
Point IdentifyRoot(List<LineString> lines, List<OutletModel> outlets)
Аргументи: lines – Список ліній, outlets – Список розеток.
Логіка: Визначає корінь як вільний кінець, який не збігається з жодною розеткою в межах OUTLET_CONNECTION_TOLERANCE.
Dictionary<OutletModel, List<LineString>> BuildPaths(Point root, List<LineString> lines, List<OutletModel> outlets)
Аргументи: root – Точка щитка, lines – Список ліній, outlets – Список розеток.
Логіка: Побудова трас від розеток до щитка, використовуючи алгоритм пошуку шляху.

## 3. RailingsGeneratorService
Відповідальність: Генерує елементи перил на основі траєкторії з дерева.
Публічні методи:
List<RailingModel> GenerateRailings(GeometryTreeModel geometryTree)
Аргументи: geometryTree – Модель дерева.
Повертає: Список RailingModel для кожної траси.
Логіка: Перебирає Paths, створює RailingModel із Lines.
Оновлена модель:
RailingModel
Властивості:
int Id – Унікальний ідентифікатор.
List<LineString> Lines – Список ліній, що визначають траєкторію перил.
int WireCount – Кількість проводів.
OutletModel SourceOutlet – Посилання на розетку.

## 4. AnnotationPlacerService
Відповідальність: Розміщує анотації на перехрестях і щитку.
Константи:
const double ANNOTATION_OFFSET = 1.58333 – Відстань 1' 7" (1 + 7/12 ≈ 1.58333 фути).
const double ANNOTATION_SIZE = 0.5 – Розмір анотації для перевірки перетинів.
Публічні методи:
List<AnnotationModel> PlaceAnnotations(List<RailingModel> railings)
Аргументи: railings – Список перил.
Повертає: Список AnnotationModel.
Логіка: Фільтрує унікальні сегменти, рекурсивно обходить дерево, створює точки зі зміщенням.
Приватні методи:
List<Point> FindIntersections(List<RailingModel> railings)
Аргументи: railings – Список перил.
Логіка: Співставляє кінцеві точки ліній, визначає перетини, додає точки зі зміщенням ANNOTATION_OFFSET у напрямку до розеток, уникає дублювання на сегментах.
Direction DetermineOrientation(Point location, List<LineString> allLines)
Аргументи: location – Точка анотації, allLines – Список ліній усіх перил.
Логіка: Перевіряє перетин анотації розміром ANNOTATION_SIZE у всіх напрямках Direction, вибирає найменш конфліктний.

## 5. WiresCalculatorService
Відповідальність: Обчислює сумарну кількість проводів для кожної анотації.
Константи:
const double PROXIMITY_THRESHOLD = 3.0 / 12.0 – Похибка 3" (≈ 0.25 фути).
Публічні методи:
void CalculateWiresTotal(List<RailingModel> railings, List<AnnotationModel> annotations)
Аргументи: railings – Список перил, annotations – Список анотацій.
Логіка: Викликає SumWiresAtPoint для кожної анотації.
Приватні методи:
int SumWiresAtPoint(Point location, List<RailingModel> railings)
Аргументи: location – Точка анотації, railings – Список перил.
Логіка: Шукає перетин location із Lines перил у межах PROXIMITY_THRESHOLD, підсумовує WireCount.

## Загальні примітки
- Послідовність: NormalizeLines → AnalyzeGeometry → GenerateRailings → PlaceAnnotations → CalculateWiresTotal.  
- Усі методи використовують абстракції, ізольовані від Revit API.