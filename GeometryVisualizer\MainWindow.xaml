<Window x:Class="GeometryVisualizer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Geometry Visualizer" Height="800" Width="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Controls -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <TextBox x:Name="FilePathTextBox" Width="400" Margin="0,0,10,0"
                     Text="geometry.json" VerticalAlignment="Center"/>
            <Button x:Name="BrowseButton" Content="Browse..." Click="BrowseButton_Click"
                    Margin="0,0,10,0" Padding="10,5"/>
            <Button x:Name="LoadButton" Content="Load" Click="LoadButton_Click"
                    Margin="0,0,10,0" Padding="10,5"/>
            <Button x:Name="ResetButton" Content="Reset View" Click="ResetButton_Click"
                    Margin="0,0,10,0" Padding="10,5"/>
            <Button x:Name="FitToWindowButton" Content="Fit to Window" Click="FitToWindowButton_Click"
                    Margin="0,0,20,0" Padding="10,5"/>
            <TextBlock Text="Zoom:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <Slider x:Name="ZoomSlider" Width="150" Minimum="0.1" Maximum="10" Value="1"
                    ValueChanged="ZoomSlider_ValueChanged" VerticalAlignment="Center"/>
            <TextBlock x:Name="ZoomLabel" Text="1.0x" VerticalAlignment="Center" Margin="5,0"/>
        </StackPanel>
        
        <!-- Canvas with ScrollViewer -->
        <ScrollViewer Grid.Row="1" x:Name="CanvasScrollViewer"
                      HorizontalScrollBarVisibility="Auto"
                      VerticalScrollBarVisibility="Auto" Background="LightGray">
            <Canvas x:Name="GeometryCanvas" Background="White"
                    MouseWheel="Canvas_MouseWheel"
                    MouseLeftButtonDown="Canvas_MouseLeftButtonDown"
                    MouseLeftButtonUp="Canvas_MouseLeftButtonUp"
                    MouseMove="Canvas_MouseMove"/>
        </ScrollViewer>
        
        <!-- Status -->
        <StatusBar Grid.Row="2">
            <TextBlock x:Name="StatusText" Text="Ready"/>
            <Separator/>
            <TextBlock x:Name="CoordinatesText" Text=""/>
        </StatusBar>
    </Grid>
</Window>