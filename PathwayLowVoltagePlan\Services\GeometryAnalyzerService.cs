using NetTopologySuite.Geometries;
using PathwayLowVoltagePlan.Models;
using System.Collections.Generic;
using System.Linq;

namespace PathwayLowVoltagePlan.Services
{
    public class GeometryAnalyzerService
    {
        private const double OUTLET_CONNECTION_TOLERANCE = 2.0; // 2 feet
        private const double LINE_CONNECTION_TOLERANCE = 1.0 / 12.0; // 1 inch

        /// <summary>
        /// Analyzes normalized lines and builds a geometry tree with paths from outlets to the panel.
        /// </summary>
        /// <param name="normalizedLines">Normalized LineStrings from LineNormalizationService.</param>
        /// <param name="outlets">List of outlet models.</param>
        /// <returns>GeometryTreeModel with root and paths.</returns>
        public GeometryTreeModel AnalyzeGeometry(List<LineString> normalizedLines, List<OutletModel> outlets)
        {
            var root = IdentifyRoot(normalizedLines, outlets);
            var paths = BuildPaths(root, normalizedLines, outlets);

            return new GeometryTreeModel
            {
                Root = root,
                Leaves = outlets,
                Paths = paths
            };
        }

        /// <summary>
        /// Identifies the root point as a free endpoint that doesn't connect to other lines or outlets.
        /// </summary>
        private Point IdentifyRoot(List<LineString> lines, List<OutletModel> outlets)
        {
            var allEndpoints = new List<Point>();
            
            foreach (var line in lines)
            {
                var coords = line.Coordinates;
                allEndpoints.Add(new Point(coords[0]));
                allEndpoints.Add(new Point(coords[coords.Length - 1]));
            }

            // Find free endpoints that don't connect to other lines or outlets
            foreach (var endpoint in allEndpoints)
            {
                // Check if connected to outlet
                bool isConnectedToOutlet = outlets.Any(outlet => 
                    outlet.Location.Distance(endpoint) <= OUTLET_CONNECTION_TOLERANCE);

                if (isConnectedToOutlet) continue;

                // Check if connected to other line endpoints
                bool isConnectedToOtherLine = allEndpoints
                    .Where(other => other != endpoint)
                    .Any(other => endpoint.Distance(other) <= LINE_CONNECTION_TOLERANCE);

                if (!isConnectedToOtherLine)
                {
                    return endpoint;
                }
            }

            return allEndpoints.FirstOrDefault() ?? Point.Empty;
        }

        /// <summary>
        /// Builds paths from each outlet to the root using pathfinding algorithm.
        /// </summary>
        private Dictionary<OutletModel, List<LineString>> BuildPaths(Point root, List<LineString> lines, List<OutletModel> outlets)
        {
            var paths = new Dictionary<OutletModel, List<LineString>>();

            foreach (var outlet in outlets)
            {
                var path = FindPath(outlet.Location, root, lines);
                if (path.Any())
                {
                    paths[outlet] = path;
                }
            }

            return paths;
        }

        /// <summary>
        /// Finds path from start point to end point using available lines.
        /// </summary>
        private List<LineString> FindPath(Point start, Point end, List<LineString> lines)
        {
            var visited = new HashSet<LineString>();
            var path = new List<LineString>();

            if (FindPathRecursive(start, end, lines, visited, path))
            {
                return path;
            }

            return new List<LineString>();
        }

        /// <summary>
        /// Recursive pathfinding algorithm.
        /// </summary>
        private bool FindPathRecursive(Point current, Point target, List<LineString> lines, 
            HashSet<LineString> visited, List<LineString> currentPath)
        {
            // Check if we reached the target
            if (current.Distance(target) <= LINE_CONNECTION_TOLERANCE)
            {
                return true;
            }

            // Try each unvisited line
            foreach (var line in lines.Where(l => !visited.Contains(l)))
            {
                var coords = line.Coordinates;
                var startPoint = new Point(coords[0]);
                var endPoint = new Point(coords[coords.Length - 1]);

                Point? nextPoint = null;

                // Check if current point connects to this line
                if (current.Distance(startPoint) <= LINE_CONNECTION_TOLERANCE)
                {
                    nextPoint = endPoint;
                }
                else if (current.Distance(endPoint) <= LINE_CONNECTION_TOLERANCE)
                {
                    nextPoint = startPoint;
                }

                if (nextPoint != null)
                {
                    visited.Add(line);
                    currentPath.Add(line);

                    if (FindPathRecursive(nextPoint, target, lines, visited, currentPath))
                    {
                        return true;
                    }

                    // Backtrack
                    currentPath.RemoveAt(currentPath.Count - 1);
                    visited.Remove(line);
                }
            }

            return false;
        }
    }
}
