﻿using NetTopologySuite.Geometries;

public class LineStringVisualizer
{
    public void CreateHtmlVisualization(List<LineString> lines, string fileName = "debug_map.html")
    {
        if (lines == null || !lines.Any())
        {
            Console.WriteLine("No lines to visualize");
            return;
        }

        // Перевірка та фільтрація валідних ліній
        var validLines = lines.Where(line => line != null && line.Coordinates.Length >= 2).ToList();
        if (!validLines.Any())
        {
            Console.WriteLine("No valid lines found");
            return;
        }

        Console.WriteLine($"Visualizing {validLines.Count} lines");

        // Обчислення bounds
        var allCoords = validLines.SelectMany(line => line.Coordinates).ToList();
        var minY = allCoords.Min(c => c.Y);
        var maxY = allCoords.Max(c => c.Y);
        var minX = allCoords.Min(c => c.X);
        var maxX = allCoords.Max(c => c.X);

        // Перевірка чи це географічні координати
        bool isGeographic = minX >= -180 && maxX <= 180 && minY >= -90 && maxY <= 90;

        Console.WriteLine($"Coordinate system: {(isGeographic ? "Geographic" : "Local/Projected")}");
        Console.WriteLine($"Bounds: X({minX:F6}, {maxX:F6}), Y({minY:F6}, {maxY:F6})");

        string html;

        if (isGeographic)
        {
            // Географічні координати - використовуємо Leaflet з OSM
            var linesDataList = new List<string>();
            for (int i = 0; i < validLines.Count; i++)
            {
                var line = validLines[i];
                var coords = string.Join(", ",
                    line.Coordinates.Select(c => $"[{c.Y:F8}, {c.X:F8}]"));
                linesDataList.Add($"{{ id: {i}, coords: [{coords}], length: {line.Length:F2} }}");
            }

            var linesData = string.Join(",\n            ", linesDataList);

            html = CreateLeafletHtml(validLines, allCoords, minX, maxX, minY, maxY, linesData);
        }
        else
        {
            // Локальні координати - використовуємо Canvas або SVG
            html = CreateSvgHtml(validLines, allCoords, minX, maxX, minY, maxY);
        }



        try
        {
            File.WriteAllText(fileName, html);
            Console.WriteLine($"HTML file created: {Path.GetFullPath(fileName)}");

            // Спроба автоматично відкрити файл
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = Path.GetFullPath(fileName),
                    UseShellExecute = true
                };
                System.Diagnostics.Process.Start(startInfo);
                Console.WriteLine("Opening in default browser...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Could not auto-open browser: {ex.Message}");
                Console.WriteLine($"Please manually open: {Path.GetFullPath(fileName)}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating HTML file: {ex.Message}");
        }
    }

    private string CreateLeafletHtml(List<LineString> validLines, List<Coordinate> allCoords,
        double minX, double maxX, double minY, double maxY, string linesData)
    {
        return $@"<!DOCTYPE html>
<html>
<head>
    <title>NetTopologySuite Lines Debug (Geographic)</title>
    <meta charset=""utf-8"" />
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <link rel=""stylesheet"" href=""https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"" />
    <style>
        body {{ margin: 0; padding: 0; }}
        #map {{ height: 100vh; width: 100vw; }}
        .info-panel {{
            position: absolute; top: 10px; right: 10px; background: white;
            padding: 10px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000; max-width: 200px;
        }}
    </style>
</head>
<body>
    <div id=""map""></div>
    <div class=""info-panel"">
        <h4>Debug Info (Geographic)</h4>
        <div id=""stats"">
            Lines: {validLines.Count}<br>Total points: {allCoords.Count}<br>
            X: {minX:F4} to {maxX:F4}<br>Y: {minY:F4} to {maxY:F4}
        </div>
        <hr><div id=""hover-info"">Hover over lines</div>
    </div>
    <script src=""https://unpkg.com/leaflet@1.9.4/dist/leaflet.js""></script>
    <script>
        var map = L.map('map').setView([{(minY + maxY) / 2}, {(minX + maxX) / 2}], 13);
        L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png').addTo(map);
        var lines = [{linesData}];
        var colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
        lines.forEach((lineData, index) => {{
            if (lineData.coords && lineData.coords.length >= 2) {{
                L.polyline(lineData.coords, {{ color: colors[index % colors.length], weight: 3 }})
                 .bindPopup(`Line ${{index}}: ${{lineData.coords.length}} points`).addTo(map);
            }}
        }});
        map.fitBounds(lines.flatMap(l => l.coords));
    </script>
</body>
</html>";
    }

    private string CreateSvgHtml(List<LineString> validLines, List<Coordinate> allCoords,
        double minX, double maxX, double minY, double maxY)
    {
        // Розміри SVG
        const int svgWidth = 1200;
        const int svgHeight = 800;
        const int padding = 50;

        // Масштабування координат в SVG простір
        double scaleX = (svgWidth - 2 * padding) / (maxX - minX);
        double scaleY = (svgHeight - 2 * padding) / (maxY - minY);
        double scale = Math.Min(scaleX, scaleY); // Зберігати пропорції

        // Центрування
        double centerX = (maxX + minX) / 2;
        double centerY = (maxY + minY) / 2;
        double svgCenterX = svgWidth / 2.0;
        double svgCenterY = svgHeight / 2.0;

        var svgLines = new List<string>();
        var colors = new[] { "#FF0000", "#00AA00", "#0000FF", "#FF8000", "#8000FF", "#FF0080",
                           "#00FF80", "#8080FF", "#FF8080", "#80FF80", "#404040", "#800080" };

        for (int i = 0; i < validLines.Count; i++)
        {
            var line = validLines[i];
            var color = colors[i % colors.Length];

            var pathData = new List<string>();
            for (int j = 0; j < line.Coordinates.Length; j++)
            {
                var coord = line.Coordinates[j];
                var x = svgCenterX + (coord.X - centerX) * scale;
                var y = svgCenterY - (coord.Y - centerY) * scale; // Інвертуємо Y для SVG

                pathData.Add(j == 0 ? $"M {x:F2} {y:F2}" : $"L {x:F2} {y:F2}");
            }

            svgLines.Add($@"
                <path d=""{string.Join(" ", pathData)}"" stroke=""{color}"" stroke-width=""2"" fill=""none""
                      data-line=""{i}"" data-points=""{line.Coordinates.Length}"" data-length=""{line.Length:F2}"" 
                      class=""line-path"" />
                <text x=""{svgCenterX + (line.Coordinates[0].X - centerX) * scale + 5}"" 
                      y=""{svgCenterY - (line.Coordinates[0].Y - centerY) * scale - 5}"" 
                      font-family=""Arial"" font-size=""12"" fill=""{color}"">L{i}</text>");

            // Додавання точок початку та кінця
            var startCoord = line.Coordinates[0];
            var endCoord = line.Coordinates[line.Coordinates.Length - 1];

            var startX = svgCenterX + (startCoord.X - centerX) * scale;
            var startY = svgCenterY - (startCoord.Y - centerY) * scale;
            var endX = svgCenterX + (endCoord.X - centerX) * scale;
            var endY = svgCenterY - (endCoord.Y - centerY) * scale;

            svgLines.Add($@"
                <circle cx=""{startX:F2}"" cy=""{startY:F2}"" r=""4"" fill=""white"" stroke=""{color}"" stroke-width=""2""
                        title=""Start of Line {i}"" />
                <circle cx=""{endX:F2}"" cy=""{endY:F2}"" r=""4"" fill=""{color}""
                        title=""End of Line {i}"" />");
        }

        return $@"<!DOCTYPE html>
<html>
<head>
    <title>NetTopologySuite Lines Debug (Local Coordinates)</title>
    <meta charset=""utf-8"" />
    <style>
        body {{ margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }}
        .container {{ display: flex; gap: 20px; }}
        .svg-container {{ background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .info-panel {{ 
            min-width: 250px; background: white; padding: 20px; border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); height: fit-content;
        }}
        .line-path {{ cursor: pointer; transition: stroke-width 0.2s; }}
        .line-path:hover {{ stroke-width: 4; }}
        #tooltip {{ 
            position: absolute; background: rgba(0,0,0,0.8); color: white; 
            padding: 8px; border-radius: 4px; font-size: 12px; pointer-events: none; 
            display: none; z-index: 1000;
        }}
        .legend {{ margin-top: 20px; }}
        .legend-item {{ display: flex; align-items: center; margin: 5px 0; }}
        .legend-color {{ width: 20px; height: 3px; margin-right: 10px; }}
        .coords {{ font-family: monospace; font-size: 11px; color: #666; max-height: 200px; overflow-y: auto; }}
    </style>
</head>
<body>
    <div class=""container"">
        <div class=""svg-container"">
            <svg width=""{svgWidth}"" height=""{svgHeight}"" viewBox=""0 0 {svgWidth} {svgHeight}""
                 style=""border: 1px solid #ddd; background: #fafafa;"">
                
                <!-- Grid -->
                <defs>
                    <pattern id=""grid"" width=""50"" height=""50"" patternUnits=""userSpaceOnUse"">
                        <path d=""M 50 0 L 0 0 0 50"" fill=""none"" stroke=""#e0e0e0"" stroke-width=""1""/>
                    </pattern>
                </defs>
                <rect width=""100%"" height=""100%"" fill=""url(#grid)"" />
                
                <!-- Coordinate system info -->
                <text x=""10"" y=""25"" font-family=""Arial"" font-size=""14"" fill=""#666"">
                    Local Coordinate System: X({minX:F1}, {maxX:F1}) Y({minY:F1}, {maxY:F1})
                </text>
                <text x=""10"" y=""45"" font-family=""Arial"" font-size=""12"" fill=""#888"">
                    Scale: {scale:F4} pixels/unit, Center: ({centerX:F2}, {centerY:F2})
                </text>

                <!-- Lines -->
                {string.Join("\n                ", svgLines)}
                
            </svg>
        </div>
        
        <div class=""info-panel"">
            <h3>Debug Info (Local Coordinates)</h3>
            <div><strong>Lines:</strong> {validLines.Count}</div>
            <div><strong>Total points:</strong> {allCoords.Count}</div>
            <div><strong>X range:</strong> {minX:F2} to {maxX:F2}</div>
            <div><strong>Y range:</strong> {minY:F2} to {maxY:F2}</div>
            <div><strong>Scale:</strong> {scale:F6}</div>
            
            <div class=""legend"">
                <h4>Lines Legend:</h4>
                {string.Join("\n                ", validLines.Select((line, i) => $@"
                <div class=""legend-item"">
                    <div class=""legend-color"" style=""background: {colors[i % colors.Length]};""></div>
                    L{i} ({line.Coordinates.Length} pts, len: {line.Length:F2})
                </div>").ToArray())}
            </div>
            
            <div id=""hover-info"" style=""margin-top: 20px; padding: 10px; background: #f9f9f9; border-radius: 4px;"">
                <strong>Hover over lines for details</strong>
            </div>
            
            <details style=""margin-top: 20px;"">
                <summary>Raw Coordinates</summary>
                <div class=""coords"">
                    {string.Join("<br>", validLines.Select((line, i) =>
                        $"<strong>L{i}:</strong> " + string.Join(" → ", line.Coordinates.Select(c => $"({c.X:F2},{c.Y:F2})"))).ToArray())}
                </div>
            </details>
        </div>
    </div>
    
    <div id=""tooltip""></div>
    
    <script>
        const tooltip = document.getElementById('tooltip');
        const hoverInfo = document.getElementById('hover-info');
        
        document.querySelectorAll('.line-path').forEach(path => {{
            path.addEventListener('mouseover', (e) => {{
                const line = e.target.getAttribute('data-line');
                const points = e.target.getAttribute('data-points');
                const length = e.target.getAttribute('data-length');
                
                hoverInfo.innerHTML = `
                    <strong>Line ${{line}}</strong><br>
                    Points: ${{points}}<br>
                    Length: ${{length}}
                `;
                
                tooltip.style.display = 'block';
                tooltip.innerHTML = `Line ${{line}}: ${{points}} points, length ${{length}}`;
            }});
            
            path.addEventListener('mousemove', (e) => {{
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
            }});
            
            path.addEventListener('mouseout', () => {{
                hoverInfo.innerHTML = '<strong>Hover over lines for details</strong>';
                tooltip.style.display = 'none';
            }});
        }});
        
        console.log('Local coordinate visualization loaded');
        console.log('Coordinate bounds:', {{ minX: {minX}, maxX: {maxX}, minY: {minY}, maxY: {maxY} }});
        console.log('SVG scale factor:', {scale});
    </script>
</body>
</html>";
    }
}
