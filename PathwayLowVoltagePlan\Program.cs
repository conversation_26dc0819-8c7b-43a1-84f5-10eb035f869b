﻿using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using PathwayLowVoltagePlan.Models;
using PathwayLowVoltagePlan.Services;

Console.WriteLine("Hello, World!");


List<LineString> lines = new List<LineString>() {
    new LineString(new Coordinate[] { new Coordinate(542.82706, 460.76553), new Coordinate(542.82706, 461.63909) }),
    new LineString(new Coordinate[] { new Coordinate(542.82706, 461.63909), new Coordinate(547.57706, 461.63909) }),
    new LineString(new Coordinate[] { new Coordinate(547.63147, 460.87951), new Coordinate(547.63147, 461.64867) }),
    new LineString(new Coordinate[] { new Coordinate(563.36520, 430.51781), new Coordinate(573.78186, 430.51781) }),
    new LineString(new Coordinate[] { new Coordinate(564.01208, 435.04060), new Coordinate(564.01208, 437.12393) }),
    new LineString(new Coordinate[] { new Coordinate(564.01208, 437.12393), new Coordinate(573.78186, 437.12393) }),
    new LineString(new Coordinate[] { new Coordinate(559.15500, 440.77533), new Coordinate(559.15500, 443.55676) }),
    new LineString(new Coordinate[] { new Coordinate(559.15500, 443.55676), new Coordinate(562.23833, 443.55676) }),
    new LineString(new Coordinate[] { new Coordinate(559.15500, 448.27533), new Coordinate(559.15500, 451.76510) }),
    new LineString(new Coordinate[] { new Coordinate(559.15500, 451.76510), new Coordinate(562.32167, 451.76510) }),
    new LineString(new Coordinate[] { new Coordinate(559.15500, 455.73367), new Coordinate(559.15500, 459.86926) }),
    new LineString(new Coordinate[] { new Coordinate(559.15500, 459.86926), new Coordinate(562.48833, 459.86926) }),
    new LineString(new Coordinate[] { new Coordinate(547.64106, 461.63909), new Coordinate(573.78186, 461.63909) }),
    new LineString(new Coordinate[] { new Coordinate(573.78186, 461.63909), new Coordinate(573.78186, 459.87781) }),
    new LineString(new Coordinate[] { new Coordinate(573.78186, 459.87781), new Coordinate(573.78186, 451.71114) }),
    new LineString(new Coordinate[] { new Coordinate(562.46443, 459.06565), new Coordinate(562.46443, 459.87885) }),
    new LineString(new Coordinate[] { new Coordinate(562.48833, 459.86926), new Coordinate(573.78186, 459.86926) }),
    new LineString(new Coordinate[] { new Coordinate(573.78186, 451.71114), new Coordinate(573.78186, 443.46114) }),
    new LineString(new Coordinate[] { new Coordinate(562.32167, 451.76510), new Coordinate(573.78186, 451.76510) }),
    new LineString(new Coordinate[] { new Coordinate(562.23833, 443.55676), new Coordinate(573.78186, 443.55676) }),
    new LineString(new Coordinate[] { new Coordinate(573.78186, 443.46114), new Coordinate(573.78186, 437.04447) }),
    new LineString(new Coordinate[] { new Coordinate(573.78186, 437.04447), new Coordinate(573.78186, 430.46114) }),
    new LineString(new Coordinate[] { new Coordinate(573.78186, 430.46114), new Coordinate(573.78186, 428.65613) }),
    new LineString(new Coordinate[] { new Coordinate(562.46443, 450.89898), new Coordinate(562.46443, 451.71218) }),
    new LineString(new Coordinate[] { new Coordinate(562.46443, 443.64898), new Coordinate(562.46443, 444.46218) }),
    new LineString(new Coordinate[] { new Coordinate(573.78186, 430.52740), new Coordinate(591.78186, 430.52740) }),
    new LineString(new Coordinate[] { new Coordinate(591.78186, 430.52740), new Coordinate(591.78186, 424.52740) })

    //new LineString(new Coordinate[] { new Coordinate(563.36520, 430.51781), new Coordinate(573.78186, 430.51781) }),
    //new LineString(new Coordinate[] { new Coordinate(564.01208, 435.04060), new Coordinate(564.01208, 437.12393) }),
    //new LineString(new Coordinate[] { new Coordinate(564.01208, 437.12393), new Coordinate(573.78186, 437.12393) }),
    //new LineString(new Coordinate[] { new Coordinate(559.15500, 440.77533), new Coordinate(559.15500, 443.55676) }),
    //new LineString(new Coordinate[] { new Coordinate(559.15500, 443.55676), new Coordinate(562.23833, 443.55676) }),
    //new LineString(new Coordinate[] { new Coordinate(559.15500, 448.27533), new Coordinate(559.15500, 451.76510) }),
    //new LineString(new Coordinate[] { new Coordinate(559.15500, 451.76510), new Coordinate(562.32167, 451.76510) }),
    //new LineString(new Coordinate[] { new Coordinate(559.15500, 455.73367), new Coordinate(559.15500, 459.86926) }),
    //new LineString(new Coordinate[] { new Coordinate(559.15500, 459.86926), new Coordinate(562.48833, 459.86926) }),
    //new LineString(new Coordinate[] { new Coordinate(573.78186, 459.87781), new Coordinate(573.78186, 451.71114) }),
    //new LineString(new Coordinate[] { new Coordinate(562.46443, 459.06565), new Coordinate(562.46443, 459.87885) }),
    //new LineString(new Coordinate[] { new Coordinate(562.48833, 459.86926), new Coordinate(573.78186, 459.86926) }),
    //new LineString(new Coordinate[] { new Coordinate(573.78186, 451.71114), new Coordinate(573.78186, 443.46114) }),
    //new LineString(new Coordinate[] { new Coordinate(562.32167, 451.76510), new Coordinate(573.78186, 451.76510) }),
    //new LineString(new Coordinate[] { new Coordinate(562.23833, 443.55676), new Coordinate(573.78186, 443.55676) }),
    //new LineString(new Coordinate[] { new Coordinate(573.78186, 443.46114), new Coordinate(573.78186, 437.04447) }),
    //new LineString(new Coordinate[] { new Coordinate(573.78186, 437.04447), new Coordinate(573.78186, 430.46114) }),
    //new LineString(new Coordinate[] { new Coordinate(573.78186, 430.46114), new Coordinate(573.78186, 428.65613) }),
    //new LineString(new Coordinate[] { new Coordinate(562.46443, 450.89898), new Coordinate(562.46443, 451.71218) }),
    //new LineString(new Coordinate[] { new Coordinate(562.46443, 443.64898), new Coordinate(562.46443, 444.46218) }),
    //new LineString(new Coordinate[] { new Coordinate(573.78186, 430.52740), new Coordinate(591.78186, 430.52740) }),
    //new LineString(new Coordinate[] { new Coordinate(591.78186, 430.52740), new Coordinate(591.78186, 424.52740) })
};

var outlets = new List<OutletModel>()
{
    new OutletModel { Id = 1, Location = new Point(543.71481, 461.15584) },
    new OutletModel { Id = 2, Location = new Point(547.63147, 461.15584) },
    new OutletModel { Id = 3, Location = new Point(559.15500, 441.05167) },
    new OutletModel { Id = 4, Location = new Point(562.91997, 443.98253) },
    new OutletModel { Id = 5, Location = new Point(559.15500, 448.55167) },
    new OutletModel { Id = 6, Location = new Point(562.91997, 451.48253) },
    new OutletModel { Id = 7, Location = new Point(559.15500, 456.01000) },
    new OutletModel { Id = 8, Location = new Point(562.91997, 459.06586) },
    new OutletModel { Id = 9, Location = new Point(563.78143, 430.57209) },
    new OutletModel { Id = 10, Location = new Point(564.01208, 435.31693) },
    new OutletModel { Id = 11, Location = new Point(591.78096, 424.11798) },
};


var fixedLines = new LineNormalizationService().NormalizeLines(lines);

fixedLines.ForEach(l => Console.WriteLine(l));

var pathes = new GeometryAnalyzerService().AnalyzeGeometry(fixedLines, outlets);

//var visualizer = new LineStringVisualizer();
//visualizer.CreateHtmlVisualization(fixedLines);

var exportService = new GeometryExportService();
//exportService.ExportToJson(fixedLines, outlets, "geometry.json");
//Console.WriteLine("Geometry exported to geometry.json");

exportService.ExportToJson(lines, outlets, "geometry.json");

Console.WriteLine("Root: " + pathes.Root);
