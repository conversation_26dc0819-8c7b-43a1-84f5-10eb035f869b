using NetTopologySuite.Geometries;
using System.Collections.Generic;

namespace PathwayLowVoltagePlan.Models
{
    public class GeometryTreeModel
    {
        public Point Root { get; set; } = Point.Empty;
        public List<OutletModel> Leaves { get; set; } = new List<OutletModel>();
        public Dictionary<OutletModel, List<LineString>> Paths { get; set; } = new Dictionary<OutletModel, List<LineString>>();
    }
}