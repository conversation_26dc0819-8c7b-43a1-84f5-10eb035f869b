using NetTopologySuite.Geometries;
using NetTopologySuite.Operation.Linemerge;
using System.Collections.Generic;
using System.Linq;
using NetTopologySuite.Operation.Distance;
using NetTopologySuite.Noding;

namespace PathwayLowVoltagePlan.Services
{
    public class LineNormalizationService
    {
        private const double TOLERANCE = 2.0 / 12.0; // 2 inches

        /// <summary>
        /// Normalizes a list of raw lines by merging, fixing undershoots, splitting, and removing short lines.
        /// </summary>
        /// <param name="rawLines">The list of raw LineStrings.</param>
        /// <returns>A list of normalized LineStrings.</returns>
        public List<LineString> NormalizeLines(List<LineString> rawLines)
        {
            var lines = MergeLines(rawLines);
            lines = FixUndershotLines(lines);
            lines = SplitLines(lines);
            lines = RemoveShortLines(lines);
            return lines;
        }

        /// <summary>
        /// Merges collinear and overlapping lines.
        /// </summary>
        private List<LineString> MergeLines(List<LineString> lines)
        {
            var merger = new LineMerger();
            merger.Add(lines);
            var merged = merger.GetMergedLineStrings();
            return merged.Cast<LineString>().ToList();
        }

        /// <summary>
        /// Extends lines to snap to other lines if they are within tolerance.
        /// </summary>
        private List<LineString> FixUndershotLines(List<LineString> lines)
        {
            var currentLines = new List<LineString>(lines);
            bool changedInIteration;

            do
            {
                changedInIteration = false;
                for (int i = 0; i < currentLines.Count; i++)
                {
                    var line1 = currentLines[i];
                    var coords = line1.Coordinates;
                    var start = coords[0];
                    var end = coords[coords.Length - 1];

                    var newStart = start.Copy();
                    var newEnd = end.Copy();

                    // Find best snap point for the start point
                    double minStartDist = TOLERANCE;
                    Coordinate? snapToForStart = null;
                    for (int j = 0; j < currentLines.Count; j++)
                    {
                        if (i == j) continue;
                        var line2 = currentLines[j];
                        var distOp = new DistanceOp(new Point(start), line2);
                        var nearestPoints = distOp.NearestPoints();
                        var dist = new Point(nearestPoints[0]).Distance(new Point(nearestPoints[1]));
                        if (dist > 1e-9 && dist < minStartDist)
                        {
                            minStartDist = dist;
                            snapToForStart = nearestPoints[1];
                        }
                    }
                    if (snapToForStart != null)
                    {
                        newStart = snapToForStart;
                    }

                    // Find best snap point for the end point
                    double minEndDist = TOLERANCE;
                    Coordinate? snapToForEnd = null;
                    for (int j = 0; j < currentLines.Count; j++)
                    {
                        if (i == j) continue;
                        var line2 = currentLines[j];
                        var distOp = new DistanceOp(new Point(end), line2);
                        var nearestPoints = distOp.NearestPoints();
                        var dist = new Point(nearestPoints[0]).Distance(new Point(nearestPoints[1]));
                        if (dist > 1e-9 && dist < minEndDist)
                        {
                            minEndDist = dist;
                            snapToForEnd = nearestPoints[1];
                        }
                    }
                    if (snapToForEnd != null)
                    {
                        newEnd = snapToForEnd;
                    }

                    // If the line was changed, update it and mark that changes were made
                    if (!newStart.Equals2D(start) || !newEnd.Equals2D(end))
                    {
                        // Avoid creating zero-length lines
                        if (newStart.Distance(newEnd) > 1e-9)
                        {
                            currentLines[i] = new LineString(new[] { newStart, newEnd });
                            changedInIteration = true;
                        }
                    }
                }
            } while (changedInIteration);

            return currentLines;
        }

        /// <summary>
        /// Splits lines at their intersection points.
        /// </summary>
        private List<LineString> SplitLines(List<LineString> lines)
        {
            // Use a noder to find and create nodes at all intersections
            var noder = new IteratedNoder(new PrecisionModel());
            var segStrings = new List<ISegmentString>();
            foreach (var line in lines)
            {
                segStrings.Add(new NodedSegmentString(line.Coordinates, null));
            }
            noder.ComputeNodes(segStrings);
            var nodedLines = noder.GetNodedSubstrings();

            var result = new List<LineString>();
            foreach (ISegmentString segment in nodedLines)
            {
                if (segment.Count > 1)
                {
                    result.Add(new LineString(segment.Coordinates));
                }
            }
            return result;
        }

        /// <summary>
        /// Removes lines that are shorter than the tolerance.
        /// </summary>
        private List<LineString> RemoveShortLines(List<LineString> lines)
        {
            return lines.Where(line => line.Length >= TOLERANCE).ToList();
        }
    }
}